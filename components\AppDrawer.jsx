"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@heroui/drawer";
import { Input } from "@heroui/input";
import { Popover, PopoverTrigger, PopoverContent } from "@heroui/popover";
import { Accordion, AccordionItem } from "@heroui/accordion";
import { useState, useMemo, useEffect } from "react";

export const usersData = [
  // A
  { name: "<PERSON>", email: "<EMAIL>", role: "Admin" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Manager" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },

  // B
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Ad<PERSON>" },
  { name: "<PERSON>", email: "<EMAIL>", role: "Moderator" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },

  // C
  { name: "<PERSON>", email: "<EMAIL>", role: "Manager" },
  { name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { name: "Cara", email: "<EMAIL>", role: "Admin" },
  { name: "Chloe", email: "<EMAIL>", role: "User" },

  // D
  { name: "Daniel", email: "<EMAIL>", role: "Admin" },
  { name: "Daisy", email: "<EMAIL>", role: "User" },
  { name: "David", email: "<EMAIL>", role: "Manager" },
  { name: "Dylan", email: "<EMAIL>", role: "User" },

  // E
  { name: "Ethan", email: "<EMAIL>", role: "Admin" },
  { name: "Ella", email: "<EMAIL>", role: "User" },
  { name: "Evelyn", email: "<EMAIL>", role: "Manager" },
  { name: "Ezra", email: "<EMAIL>", role: "User" },

  // F
  { name: "Faith", email: "<EMAIL>", role: "User" },
  { name: "Finn", email: "<EMAIL>", role: "Admin" },
  { name: "Fiona", email: "<EMAIL>", role: "User" },
  { name: "Felix", email: "<EMAIL>", role: "Moderator" },

  // G
  { name: "Gabriel", email: "<EMAIL>", role: "Manager" },
  { name: "Grace", email: "<EMAIL>", role: "User" },
  { name: "Gavin", email: "<EMAIL>", role: "Admin" },
  { name: "Georgia", email: "<EMAIL>", role: "User" },

  // H
  { name: "Hannah", email: "<EMAIL>", role: "User" },
  { name: "Henry", email: "<EMAIL>", role: "Admin" },
  { name: "Hazel", email: "<EMAIL>", role: "Manager" },
  { name: "Hunter", email: "<EMAIL>", role: "User" },

  // I
  { name: "Ian", email: "<EMAIL>", role: "User" },
  { name: "Isla", email: "<EMAIL>", role: "Manager" },
  { name: "Ivy", email: "<EMAIL>", role: "User" },
  { name: "Isaac", email: "<EMAIL>", role: "Admin" },

  // J
  { name: "Jack", email: "<EMAIL>", role: "Admin" },
  { name: "Jade", email: "<EMAIL>", role: "User" },
  { name: "James", email: "<EMAIL>", role: "Manager" },
  { name: "Julia", email: "<EMAIL>", role: "User" },

  // K
  { name: "Kai", email: "<EMAIL>", role: "User" },
  { name: "Kylie", email: "<EMAIL>", role: "User" },
  { name: "Kevin", email: "<EMAIL>", role: "Admin" },
  { name: "Kate", email: "<EMAIL>", role: "Manager" },

  // L
  { name: "Liam", email: "<EMAIL>", role: "Manager" },
  { name: "Lily", email: "<EMAIL>", role: "User" },
  { name: "Lucas", email: "<EMAIL>", role: "Admin" },
  { name: "Leah", email: "<EMAIL>", role: "User" },

  // M
  { name: "Mason", email: "<EMAIL>", role: "User" },
  { name: "Mia", email: "<EMAIL>", role: "User" },
  { name: "Miles", email: "<EMAIL>", role: "Admin" },
  { name: "Molly", email: "<EMAIL>", role: "Manager" },

  // N
  { name: "Noah", email: "<EMAIL>", role: "Admin" },
  { name: "Nora", email: "<EMAIL>", role: "User" },
  { name: "Nathan", email: "<EMAIL>", role: "Manager" },
  { name: "Naomi", email: "<EMAIL>", role: "User" },

  // O
  { name: "Olivia", email: "<EMAIL>", role: "User" },
  { name: "Oscar", email: "<EMAIL>", role: "Admin" },
  { name: "Owen", email: "<EMAIL>", role: "Manager" },
  { name: "Ophelia", email: "<EMAIL>", role: "User" },

  // P
  { name: "Paul", email: "<EMAIL>", role: "Moderator" },
  { name: "Piper", email: "<EMAIL>", role: "User" },
  { name: "Peter", email: "<EMAIL>", role: "Admin" },
  { name: "Paige", email: "<EMAIL>", role: "User" },

  // Q
  { name: "Quinn", email: "<EMAIL>", role: "User" },
  { name: "Quentin", email: "<EMAIL>", role: "Manager" },
  { name: "Queenie", email: "<EMAIL>", role: "User" },
  { name: "Quincy", email: "<EMAIL>", role: "Admin" },

  // R
  { name: "Ryan", email: "<EMAIL>", role: "Manager" },
  { name: "Ruby", email: "<EMAIL>", role: "User" },
  { name: "Rebecca", email: "<EMAIL>", role: "Admin" },
  { name: "Riley", email: "<EMAIL>", role: "User" },

  // S
  { name: "Sophia", email: "<EMAIL>", role: "Admin" },
  { name: "Samuel", email: "<EMAIL>", role: "User" },
  { name: "Scarlett", email: "<EMAIL>", role: "Manager" },
  { name: "Sean", email: "<EMAIL>", role: "User" },

  // T
  { name: "Thomas", email: "<EMAIL>", role: "User" },
  { name: "Theo", email: "<EMAIL>", role: "Manager" },
  { name: "Tessa", email: "<EMAIL>", role: "User" },
  { name: "Travis", email: "<EMAIL>", role: "Admin" },

  // U
  { name: "Uma", email: "<EMAIL>", role: "Manager" },
  { name: "Ursula", email: "<EMAIL>", role: "User" },
  { name: "Ulrich", email: "<EMAIL>", role: "Admin" },
  { name: "Uriah", email: "<EMAIL>", role: "User" },

  // V
  { name: "Victor", email: "<EMAIL>", role: "User" },
  { name: "Violet", email: "<EMAIL>", role: "Manager" },
  { name: "Vanessa", email: "<EMAIL>", role: "User" },
  { name: "Vincent", email: "<EMAIL>", role: "Admin" },

  // W
  { name: "William", email: "<EMAIL>", role: "Admin" },
  { name: "Willow", email: "<EMAIL>", role: "User" },
  { name: "Wesley", email: "<EMAIL>", role: "Manager" },
  { name: "Wendy", email: "<EMAIL>", role: "User" },

  // X
  { name: "Xander", email: "<EMAIL>", role: "User" },
  { name: "Xavier", email: "<EMAIL>", role: "Admin" },
  { name: "Ximena", email: "<EMAIL>", role: "Manager" },
  { name: "Xyla", email: "<EMAIL>", role: "User" },

  // Y
  { name: "Yara", email: "<EMAIL>", role: "Manager" },
  { name: "Yvonne", email: "<EMAIL>", role: "User" },
  { name: "Yosef", email: "<EMAIL>", role: "Admin" },
  { name: "Yvette", email: "<EMAIL>", role: "User" },

  // Z
  { name: "Zoe", email: "<EMAIL>", role: "User" },
  { name: "Zach", email: "<EMAIL>", role: "Manager" },
  { name: "Zara", email: "<EMAIL>", role: "User" },
  { name: "Zane", email: "<EMAIL>", role: "Admin" },
];

export default function AppDrawer({ isOpen, onClose }) {
  const [search, setSearch] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  const [openKeys, setOpenKeys] = useState(new Set());

  useEffect(() => {
    const checkScreenSize = () => setIsMobile(window.innerWidth < 640);
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const filteredUsers = useMemo(() => {
    return usersData
      .filter((user) => user.name.toLowerCase().includes(search.toLowerCase()))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [search]);

  const groupedUsers = useMemo(() => {
    return filteredUsers.reduce((groups, user) => {
      const firstLetter = user.name.charAt(0).toUpperCase();
      if (!groups[firstLetter]) {
        groups[firstLetter] = [];
      }
      groups[firstLetter].push(user);
      return groups;
    }, {});
  }, [filteredUsers]);

  useEffect(() => {
    if (search.trim() === "") {
      const firstLetter = Object.keys(groupedUsers)[0];
      setOpenKeys(firstLetter ? new Set([firstLetter]) : new Set());
    } else {
      setOpenKeys(new Set(Object.keys(groupedUsers)));
    }
  }, [search, groupedUsers]);

  return (
    <Drawer isOpen={isOpen} onOpenChange={onClose} placement="right" size="sm">
      <DrawerContent className="h-[70vh] top-[65px] rounded-xl">
        {() => (
          <>
            <DrawerHeader className="flex flex-col gap-2">
              <span className="text-lg font-semibold">Member List</span>
              <Input
                placeholder="Search users..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                clearable
                className="w-full"
              />
            </DrawerHeader>

            <DrawerBody>
              {Object.keys(groupedUsers).length > 0 ? (
                <Accordion
                  selectedKeys={openKeys}
                  onSelectionChange={(keys) => setOpenKeys(keys)}
                  showDivider={false}
                >
                  {Object.keys(groupedUsers).map((letter) => (
                    <AccordionItem
                      key={letter}
                      aria-label={letter}
                      title={letter}
                      classNames={{
                        heading: "bg-gray-200 text-white rounded-lg mb-2",
                        trigger: "px-4 py-2",
                        content: "bg-gray-50 p-2 rounded-lg",
                      }}
                    >
                      <ul className="space-y-1">
                        {groupedUsers[letter].map((user, index) => (
                          <li key={index}>
                            <Popover
                              placement={
                                isMobile ? "bottom-start" : "right-start"
                              }
                            >
                              <PopoverTrigger>
                                <div className="p-2 bg-gray-100 rounded hover:bg-gray-200 cursor-pointer">
                                  {user.name}
                                </div>
                              </PopoverTrigger>
                              <PopoverContent className="p-4 max-w-[250px] sm:max-w-xs">
                                <div>
                                  <h4 className="font-semibold">{user.name}</h4>
                                  <p className="text-sm text-gray-600">
                                    Email: {user.email}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    Role: {user.role}
                                  </p>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </li>
                        ))}
                      </ul>
                    </AccordionItem>
                  ))}
                </Accordion>
              ) : (
                <p className="text-gray-400 text-sm">No users found</p>
              )}
            </DrawerBody>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
}
