"use client";
import React from "react";
import Slider from "react-slick";
import { Card, CardBody } from "@heroui/card";
import { Link } from "@heroui/link";
import { useLanguage } from "@/contexts/LanguageContext";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const teamMembers = [
  {
    name: "GEORGE S K",
    role: "Partner",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "ADAM HUSBAND",
    role: "Director (Consultancy)",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "<PERSON> KURUVI<PERSON>",
    role: "Chief Operations Officer",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "ABDELRAHMAN ELHADI",
    role: "Senior Legal Advisor",
    image: "https://via.placeholder.com/400x500",
  },
  {
    name: "<PERSON>AN<PERSON> DOE",
    role: "Marketing Manager",
    image: "https://via.placeholder.com/400x500",
  },
];

export default function TeamCards() {
  const { t } = useLanguage();

  const settings = {
    dots: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 3000,
    speed: 800,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: 3 } },
      { breakpoint: 768, settings: { slidesToShow: 2 } },
      { breakpoint: 480, settings: { slidesToShow: 1 } },
    ],
  };

  return (
    <>
      <div className="text-center mb-16">
        <h2 className="text-3xl lg:text-4xl font-medium font-serif text-primary mb-4">
          Team Members
        </h2>
        <p className="text-xl text-slate-600 max-w-3xl mx-auto">
          {t("pdfResources.subtitle")}
        </p>
      </div>

      <div className="bg-gray-100 p-6 overflow-hidden">
        <Slider {...settings}>
          {teamMembers.map((member, index) => (
            <div key={index} className="px-2">
              <Card className="bg-[#FAF5F5] shadow-lg rounded-lg border-primary border-[3px]">
                <CardBody className="relative p-0">
                  <img
                    src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSiPICw0MvHj_wo8CMy5anGtsvBzqAJSQtDFw&s"
                    alt={member.name}
                    className="h-[500px] min-h-[500px] w-full object-cover"
                  />
                  <div className="flex flex-row items-center justify-between absolute bottom-0 bg-primary bg-opacity-60 backdrop-blur-md w-full p-4">
                    <div>
                      <p className="text-lg text-white font-bold">
                        {member.name}
                      </p>
                      <p className="text-base text-white">{member.role}</p>
                    </div>
                    <Link href="mailto:<EMAIL>">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-8 h-8"
                        fill="none"
                        viewBox="0 0 30 30"
                        stroke="white"
                        strokeWidth={2}
                      >
                        <path d="M4 4h22v22H4z" />
                        <polyline points="4,4 15,15 26,4" />
                      </svg>
                    </Link>
                  </div>
                </CardBody>
              </Card>
            </div>
          ))}
        </Slider>
      </div>
    </>
  );
}
